import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
  Dimensions,
  SafeAreaView,
  Modal,
  TextInput,
  ImageBackground,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import Animated, {
  FadeInUp,
  SlideInLeft,
  FadeInDown,
  SlideInUp,
  SlideInRight,
  ZoomIn,
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from 'react-native-reanimated';
import ApiService from '../services/ApiService';

const { width, height } = Dimensions.get('window');

// Types
interface MealPlan {
  day: string;
  meals: {
    breakfast: { name: string; calories: number; time: string };
    lunch: { name: string; calories: number; time: string };
    dinner: { name: string; calories: number; time: string };
    snack?: { name: string; calories: number; time: string };
  };
  completed: boolean;
}

interface WeekPlan {
  week: Array<{
    day: string;
    meals: {
      breakfast: string;
      lunch: string;
      dinner: string;
      snack?: string;
    };
  }>;
}

const PlanScreenModern: React.FC = () => {
  // Core states
  const [weekPlan, setWeekPlan] = useState<WeekPlan | null>(null);
  const [loading, setLoading] = useState(false);
  const [loadingType, setLoadingType] = useState<string | null>(null);
  const [selectedDay, setSelectedDay] = useState<string | null>(null);
  const [showCustomization, setShowCustomization] = useState(false);
  const [viewMode, setViewMode] = useState<'week' | 'day' | 'timeline'>('week');

  // Voice recognition states
  const [isListening, setIsListening] = useState(false);
  const [voiceText, setVoiceText] = useState('');

  // Customization states
  const [customPrompt, setCustomPrompt] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [calorieTarget, setCalorieTarget] = useState(2000);
  const [proteinTarget, setProteinTarget] = useState(150);
  const [mealCount, setMealCount] = useState(3);
  const [cookingTime, setCookingTime] = useState('30');
  const [budgetLevel, setBudgetLevel] = useState('medium');
  const [cuisinePreferences, setCuisinePreferences] = useState<string[]>([]);
  const [allergens, setAllergens] = useState<string[]>([]);
  const [dietaryStyle, setDietaryStyle] = useState('balanced');
  const [activityLevel, setActivityLevel] = useState('moderate');
  const [healthGoals, setHealthGoals] = useState<string[]>([]);

  // Animation values
  const cardScale = useSharedValue(1);
  const modalOpacity = useSharedValue(0);

  const availableTags = [
    { id: 'vegetarian', label: 'Vegetarian', icon: 'leaf' },
    { id: 'vegan', label: 'Vegan', icon: 'flower' },
    { id: 'keto', label: 'Keto', icon: 'flame' },
    { id: 'low-carb', label: 'Low Carb', icon: 'trending-down' },
    { id: 'high-protein', label: 'High Protein', icon: 'fitness' },
    { id: 'gluten-free', label: 'Gluten Free', icon: 'shield-checkmark' },
    { id: 'dairy-free', label: 'Dairy Free', icon: 'water' },
    { id: 'budget-friendly', label: 'Budget Friendly', icon: 'wallet' },
    { id: 'quick-meals', label: 'Quick Meals', icon: 'time' },
    { id: 'meal-prep', label: 'Meal Prep', icon: 'cube' },
    { id: 'anti-inflammatory', label: 'Anti-Inflammatory', icon: 'medical' },
    { id: 'heart-healthy', label: 'Heart Healthy', icon: 'heart' },
    { id: 'weight-loss', label: 'Weight Loss', icon: 'trending-down' },
    { id: 'muscle-gain', label: 'Muscle Gain', icon: 'barbell' },
    { id: 'energy-boost', label: 'Energy Boost', icon: 'flash' },
  ];

  // Generate meal plan function with customization
  const generateWeeklyPlan = async () => {
    setLoading(true);
    setLoadingType('plan');
    try {
      // Build comprehensive goal string with user preferences
      let goal = 'Create a weekly meal plan with ONLY meal names';

      if (customPrompt.trim()) {
        goal += ` focusing on: ${customPrompt.trim()}`;
      } else {
        goal += ' for optimal health and nutrition';
      }

      if (selectedTags.length > 0) {
        const tagLabels = selectedTags.map(tagId =>
          availableTags.find(tag => tag.id === tagId)?.label || tagId
        );
        goal += `. Dietary preferences: ${tagLabels.join(', ')}`;
      }

      goal += `. Requirements:
      - Target ${calorieTarget} calories per day
      - Target ${proteinTarget}g protein daily
      - Plan for ${mealCount} meals per day
      - Maximum cooking time: ${cookingTime} minutes per meal
      - Budget level: ${budgetLevel}
      - Dietary style: ${dietaryStyle}
      - Activity level: ${activityLevel}`;

      if (cuisinePreferences.length > 0) {
        goal += `\n- Preferred cuisines: ${cuisinePreferences.join(', ')}`;
      }

      if (allergens.length > 0) {
        goal += `\n- CRITICAL: MUST AVOID these allergens: ${allergens.join(', ')}`;
      }

      if (healthGoals.length > 0) {
        goal += `\n- Health goals: ${healthGoals.join(', ')}`;
      }

      goal += `\n\nCRITICAL: Return ONLY meal names (2-4 words each). NO recipes, NO ingredients, NO cooking instructions.`;

      console.log('🍽️ Generating personalized meal plan...');

      let result;
      try {
        result = await ApiService.generateMealPlan(goal);
      } catch (apiError) {
        console.error('API Error generating meal plan:', apiError);
        Alert.alert(
          'Meal Plan Generation Failed',
          'Unable to generate meal plan. Please check your internet connection and try again.',
          [
            { text: 'OK', style: 'default' },
            { text: 'Retry', onPress: () => generateWeeklyPlan() }
          ]
        );
        return;
      }

      if (result && result.week) {
        setWeekPlan(result);
        console.log('✅ Personalized meal plan generated successfully');
      } else {
        throw new Error('Invalid meal plan structure received');
      }
    } catch (error) {
      console.error('Error generating meal plan:', error);
      Alert.alert(
        'Error',
        'Failed to generate meal plan. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setLoading(false);
      setLoadingType(null);
    }
  };

  // Handle meal press for recipe details
  const handleMealPress = (mealType: string, meal: any) => {
    console.log('Meal pressed:', mealType, meal);
    // Navigate to recipe detail screen
    // navigation.navigate('RecipeDetail', { mealName: meal.name || meal });
  };

  // Generate alternative meal
  const generateAlternative = async (mealType: string, currentMeal: string) => {
    setLoadingType(`${mealType}-alternative`);
    try {
      // Generate alternative meal with same preferences
      const goal = `Generate ONE alternative ${mealType} meal name similar to "${currentMeal}" with same dietary preferences: ${selectedTags.join(', ')}. Return ONLY the meal name.`;

      // This would call a specific API for single meal generation
      console.log(`🔄 Generating alternative for ${currentMeal}...`);

      // For now, just show success message
      Alert.alert('Alternative Generated', `New ${mealType} option will be available soon!`);
    } catch (error) {
      console.error('Error generating alternative:', error);
      Alert.alert('Error', 'Failed to generate alternative meal.');
    } finally {
      setLoadingType(null);
    }
  };

  // Modern meal card component with options
  const ModernMealCard: React.FC<{
    meal: { name: string; calories: number; time: string };
    type: string;
    icon: string;
    index: number;
  }> = ({ meal, type, icon, index }) => {
    const [showOptions, setShowOptions] = useState(false);

    return (
      <Animated.View
        entering={SlideInLeft.delay(index * 100).duration(600)}
        style={styles.modernMealCard}
      >
        <LinearGradient
          colors={['rgba(255, 255, 255, 0.95)', 'rgba(255, 255, 255, 0.85)']}
          style={styles.mealCardGradient}
        >
          <View style={styles.mealCardHeader}>
            <View style={styles.modernMealIcon}>
              <LinearGradient
                colors={['#6B7C5A', '#8B9A7A']}
                style={styles.iconGradient}
              >
                <Ionicons name={icon as any} size={20} color="white" />
              </LinearGradient>
            </View>
            <View style={styles.mealCardInfo}>
              <Text style={styles.modernMealType}>{type}</Text>
              <Text style={styles.modernMealTime}>{meal.time}</Text>
            </View>
            <View style={styles.mealStats}>
              <Text style={styles.calorieCount}>{meal.calories}</Text>
              <Text style={styles.calorieLabel}>cal</Text>
            </View>
          </View>

          <Text style={styles.modernMealName}>{meal.name}</Text>

          <View style={styles.mealActions}>
            <TouchableOpacity
              style={styles.primaryAction}
              onPress={() => handleMealPress(type, meal)}
            >
              <LinearGradient
                colors={['#6B7C5A', '#8B9A7A']}
                style={styles.actionGradient}
              >
                <Ionicons name="restaurant" size={16} color="white" />
                <Text style={styles.actionText}>Recipe</Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.secondaryAction}
              onPress={() => generateAlternative(type, meal.name)}
              disabled={loadingType === `${type}-alternative`}
            >
              {loadingType === `${type}-alternative` ? (
                <ActivityIndicator size="small" color="#6B7C5A" />
              ) : (
                <>
                  <Ionicons name="refresh" size={16} color="#6B7C5A" />
                  <Text style={styles.secondaryActionText}>Alternative</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  // Modern day card component
  const ModernDayCard: React.FC<{ plan: any; delay: number }> = ({ plan, delay }) => {
    const isSelected = selectedDay === plan.day;

    return (
      <Animated.View entering={FadeInUp.delay(delay).duration(600)}>
        <TouchableOpacity
          style={[styles.modernDayCard, isSelected && styles.modernDayCardSelected]}
          onPress={() => setSelectedDay(isSelected ? null : plan.day)}
        >
          <LinearGradient
            colors={isSelected ? ['#6B7C5A', '#8B9A7A'] : ['rgba(255, 255, 255, 0.95)', 'rgba(255, 255, 255, 0.85)']}
            style={styles.dayCardGradient}
          >
            <View style={styles.modernDayHeader}>
              <View style={styles.dayInfo}>
                <Text style={[styles.modernDayName, isSelected && styles.modernDayNameSelected]}>
                  {plan.day}
                </Text>
                <Text style={[styles.dayMealCount, isSelected && styles.dayMealCountSelected]}>
                  {Object.keys(plan.meals).length} meals
                </Text>
              </View>
              <View style={styles.dayActions}>
                <View style={[styles.dayCalories, isSelected && styles.dayCaloriesSelected]}>
                  <Text style={[styles.calorieNumber, isSelected && styles.calorieNumberSelected]}>
                    1,300
                  </Text>
                  <Text style={[styles.calorieText, isSelected && styles.calorieTextSelected]}>
                    cal
                  </Text>
                </View>
                <Ionicons
                  name={isSelected ? "chevron-up" : "chevron-down"}
                  size={24}
                  color={isSelected ? "white" : "#6B7C5A"}
                />
              </View>
            </View>

            {isSelected && (
              <Animated.View entering={FadeInDown.duration(400)} style={styles.modernDayDetails}>
                <ModernMealCard
                  meal={{ name: plan.meals.breakfast, calories: 350, time: "8:00 AM" }}
                  type="Breakfast"
                  icon="sunny"
                  index={0}
                />
                <ModernMealCard
                  meal={{ name: plan.meals.lunch, calories: 450, time: "12:30 PM" }}
                  type="Lunch"
                  icon="restaurant"
                  index={1}
                />
                <ModernMealCard
                  meal={{ name: plan.meals.dinner, calories: 500, time: "7:00 PM" }}
                  type="Dinner"
                  icon="moon"
                  index={2}
                />
                {plan.meals.snack && (
                  <ModernMealCard
                    meal={{ name: plan.meals.snack, calories: 150, time: "3:00 PM" }}
                    type="Snack"
                    icon="cafe"
                    index={3}
                  />
                )}
              </Animated.View>
            )}
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#6B7C5A" />

      {/* Modern Header with Gradient */}
      <LinearGradient
        colors={['#6B7C5A', '#8B9A7A']}
        style={styles.modernHeader}
      >
        <SafeAreaView>
          <Animated.View entering={FadeInUp.duration(600)} style={styles.headerContent}>
            <View style={styles.headerTop}>
              <View>
                <Text style={styles.modernTitle}>Meal Plans</Text>
                <Text style={styles.modernSubtitle}>Your personalized nutrition journey</Text>
              </View>
              <TouchableOpacity
                style={styles.headerAction}
                onPress={() => setShowCustomization(true)}
              >
                <Ionicons name="settings" size={24} color="white" />
              </TouchableOpacity>
            </View>

            {/* Quick Stats */}
            <Animated.View entering={SlideInRight.delay(200).duration(600)} style={styles.quickStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>7</Text>
                <Text style={styles.statLabel}>Days</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>21</Text>
                <Text style={styles.statLabel}>Meals</Text>
              </View>
              <View style={styles.statDivider} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>1,800</Text>
                <Text style={styles.statLabel}>Avg Cal</Text>
              </View>
            </Animated.View>
          </Animated.View>
        </SafeAreaView>
      </LinearGradient>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Action Buttons */}
        <Animated.View entering={SlideInUp.delay(300).duration(600)} style={styles.actionSection}>
          <View style={styles.actionGrid}>
            <TouchableOpacity
              style={[styles.modernCreateButton, loading && styles.modernCreateButtonDisabled]}
              onPress={generateWeeklyPlan}
              disabled={loading}
            >
              <LinearGradient
                colors={loading ? ['#9CA3AF', '#9CA3AF'] : ['#6B7C5A', '#8B9A7A']}
                style={styles.createButtonGradient}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <Ionicons name="add-circle" size={28} color="white" />
                )}
                <Text style={styles.modernCreateText}>
                  {loading ? 'Generating...' : 'Create Plan'}
                </Text>
              </LinearGradient>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modernCustomizeButton}
              onPress={() => setShowCustomization(true)}
            >
              <View style={styles.customizeButtonContent}>
                <Ionicons name="options" size={24} color="#6B7C5A" />
                <Text style={styles.customizeText}>Customize</Text>
              </View>
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Meal Plan Display */}
        {weekPlan ? (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernPlanContainer}>
            <View style={styles.planHeader}>
              <Text style={styles.modernPlanTitle}>This Week's Plan</Text>
              <View style={styles.viewModeToggle}>
                <TouchableOpacity
                  style={[styles.toggleButton, viewMode === 'week' && styles.toggleButtonActive]}
                  onPress={() => setViewMode('week')}
                >
                  <Ionicons name="calendar" size={16} color={viewMode === 'week' ? 'white' : '#6B7C5A'} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.toggleButton, viewMode === 'day' && styles.toggleButtonActive]}
                  onPress={() => setViewMode('day')}
                >
                  <Ionicons name="today" size={16} color={viewMode === 'day' ? 'white' : '#6B7C5A'} />
                </TouchableOpacity>
              </View>
            </View>

            {weekPlan.week.map((dayPlan, index) => (
              <ModernDayCard key={dayPlan.day} plan={dayPlan} delay={index * 100} />
            ))}
          </Animated.View>
        ) : (
          <Animated.View entering={FadeInUp.delay(400).duration(600)} style={styles.modernEmptyState}>
            <LinearGradient
              colors={['rgba(107, 124, 90, 0.1)', 'rgba(107, 124, 90, 0.05)']}
              style={styles.emptyStateGradient}
            >
              <Ionicons name="restaurant-outline" size={80} color="#6B7C5A" />
              <Text style={styles.modernEmptyTitle}>Ready to Plan Your Week?</Text>
              <Text style={styles.modernEmptySubtitle}>
                Create a personalized meal plan tailored to your goals and preferences
              </Text>
              <TouchableOpacity
                style={styles.emptyStateButton}
                onPress={generateWeeklyPlan}
              >
                <LinearGradient
                  colors={['#6B7C5A', '#8B9A7A']}
                  style={styles.emptyButtonGradient}
                >
                  <Ionicons name="add" size={20} color="white" />
                  <Text style={styles.emptyButtonText}>Get Started</Text>
                </LinearGradient>
              </TouchableOpacity>
            </LinearGradient>
          </Animated.View>
        )}
      </ScrollView>

      {/* Customize Modal */}
      <Modal
        visible={showCustomization}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCustomization(false)}
        statusBarTranslucent={true}
      >
        <View style={styles.modalBlur}>
          <View style={styles.modalOverlay}>
            <Animated.View
              entering={SlideInUp.duration(600)}
              style={styles.modernModalContainer}
            >
              <LinearGradient
                colors={['#FFFFFF', '#F8F9FA']}
                style={styles.modalGradient}
              >
                {/* Modal Header */}
                <View style={styles.modernModalHeader}>
                  <View style={styles.modalHeaderContent}>
                    <Text style={styles.modernModalTitle}>🎯 Customize Your Plan</Text>
                    <Text style={styles.modernModalSubtitle}>Personalize your nutrition journey</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.modernModalClose}
                    onPress={() => setShowCustomization(false)}
                  >
                    <Ionicons name="close" size={24} color="#6B7C5A" />
                  </TouchableOpacity>
                </View>

                <ScrollView
                  style={styles.modernModalContent}
                  showsVerticalScrollIndicator={false}
                >
                  {/* Goals Section */}
                  <View style={styles.modernSection}>
                    <Text style={styles.modernSectionTitle}>🎯 Your Goals</Text>
                    <TextInput
                      style={styles.modernTextInput}
                      placeholder="e.g., lose weight, build muscle, improve energy..."
                      value={customPrompt}
                      onChangeText={setCustomPrompt}
                      multiline
                      placeholderTextColor="#9CA3AF"
                    />
                  </View>

                  {/* Nutrition Targets */}
                  <View style={styles.modernSection}>
                    <Text style={styles.modernSectionTitle}>📊 Nutrition Targets</Text>
                    <View style={styles.nutritionGrid}>
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Daily Calories</Text>
                        <TextInput
                          style={styles.nutritionInput}
                          value={calorieTarget.toString()}
                          onChangeText={(text) => setCalorieTarget(parseInt(text) || 2000)}
                          keyboardType="numeric"
                          placeholderTextColor="#9CA3AF"
                        />
                      </View>
                      <View style={styles.nutritionItem}>
                        <Text style={styles.nutritionLabel}>Protein (g)</Text>
                        <TextInput
                          style={styles.nutritionInput}
                          value={proteinTarget.toString()}
                          onChangeText={(text) => setProteinTarget(parseInt(text) || 150)}
                          keyboardType="numeric"
                          placeholderTextColor="#9CA3AF"
                        />
                      </View>
                    </View>
                  </View>

                  {/* Dietary Preferences */}
                  <View style={styles.modernSection}>
                    <Text style={styles.modernSectionTitle}>🥗 Dietary Preferences</Text>
                    <View style={styles.modernTagsGrid}>
                      {availableTags.slice(0, 8).map((tag) => (
                        <TouchableOpacity
                          key={tag.id}
                          style={[
                            styles.modernTag,
                            selectedTags.includes(tag.id) && styles.modernTagSelected
                          ]}
                          onPress={() => {
                            setSelectedTags(prev =>
                              prev.includes(tag.id)
                                ? prev.filter(t => t !== tag.id)
                                : [...prev, tag.id]
                            );
                          }}
                        >
                          <Ionicons
                            name={tag.icon as any}
                            size={16}
                            color={selectedTags.includes(tag.id) ? '#FFFFFF' : '#6B7C5A'}
                          />
                          <Text style={[
                            styles.modernTagText,
                            selectedTags.includes(tag.id) && styles.modernTagTextSelected
                          ]}>
                            {tag.label}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  {/* Meal Preferences */}
                  <View style={styles.modernSection}>
                    <Text style={styles.modernSectionTitle}>🍽️ Meal Preferences</Text>
                    <View style={styles.preferenceRow}>
                      <Text style={styles.preferenceLabel}>Meals per day:</Text>
                      <View style={styles.modernCountButtons}>
                        {[3, 4, 5, 6].map((count) => (
                          <TouchableOpacity
                            key={count}
                            style={[
                              styles.modernCountButton,
                              mealCount === count && styles.modernCountButtonSelected
                            ]}
                            onPress={() => setMealCount(count)}
                          >
                            <Text style={[
                              styles.modernCountText,
                              mealCount === count && styles.modernCountTextSelected
                            ]}>
                              {count}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  </View>
                </ScrollView>

                {/* Modal Actions */}
                <View style={styles.modernModalActions}>
                  <TouchableOpacity
                    style={styles.modernCancelButton}
                    onPress={() => setShowCustomization(false)}
                  >
                    <Text style={styles.modernCancelText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.modernSaveButton}
                    onPress={() => {
                      setShowCustomization(false);
                      generateWeeklyPlan();
                    }}
                  >
                    <LinearGradient
                      colors={['#6B7C5A', '#8B9A7A']}
                      style={styles.saveButtonGradient}
                    >
                      <Text style={styles.modernSaveText}>Generate Plan</Text>
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </LinearGradient>
            </Animated.View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = {
  // Base Container
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
  },

  // Modern Header
  modernHeader: {
    paddingBottom: 20,
    backgroundColor: '#6B7C5A',
  },
  headerContent: {
    padding: 20,
  },
  headerTop: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    marginBottom: 20,
  },
  modernTitle: {
    fontSize: 32,
    fontWeight: '800' as const,
    color: 'white',
    marginBottom: 4,
  },
  modernSubtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  headerAction: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },

  // Quick Stats
  quickStats: {
    flexDirection: 'row' as const,
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center' as const,
    justifyContent: 'space-around' as const,
  },
  statItem: {
    alignItems: 'center' as const,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: 'white',
  },
  statLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },

  // Action Section
  actionSection: {
    padding: 20,
    paddingTop: 0,
  },
  actionGrid: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  modernCreateButton: {
    flex: 2,
    borderRadius: 20,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  modernCreateButtonDisabled: {
    opacity: 0.7,
  },
  createButtonGradient: {
    padding: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    flexDirection: 'row' as const,
    gap: 8,
    backgroundColor: '#6B7C5A',
  },
  modernCreateText: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: 'white',
  },
  modernCustomizeButton: {
    flex: 1,
    backgroundColor: '#8B9A7A',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 6,
  },
  customizeButtonContent: {
    alignItems: 'center' as const,
    gap: 4,
  },
  customizeText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: 'white',
  },

  // Plan Container
  modernPlanContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  planHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 20,
  },
  modernPlanTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
  },
  viewModeToggle: {
    flexDirection: 'row' as const,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    padding: 4,
  },
  toggleButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  toggleButtonActive: {
    backgroundColor: '#6B7C5A',
  },

  // Modern Day Card
  modernDayCard: {
    marginBottom: 16,
    borderRadius: 20,
    overflow: 'hidden' as const,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  modernDayCardSelected: {
    shadowOpacity: 0.2,
    shadowRadius: 20,
    elevation: 12,
  },
  dayCardGradient: {
    padding: 20,
  },
  modernDayHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  dayInfo: {
    flex: 1,
  },
  modernDayName: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginBottom: 4,
  },
  modernDayNameSelected: {
    color: 'white',
  },
  dayMealCount: {
    fontSize: 14,
    color: '#6B7280',
  },
  dayMealCountSelected: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  dayActions: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 16,
  },
  dayCalories: {
    alignItems: 'center' as const,
  },
  dayCaloriesSelected: {
    // No additional styles needed
  },
  calorieNumber: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#6B7C5A',
  },
  calorieNumberSelected: {
    color: 'white',
  },
  calorieText: {
    fontSize: 12,
    color: '#6B7280',
  },
  calorieTextSelected: {
    color: 'rgba(255, 255, 255, 0.8)',
  },
  modernDayDetails: {
    marginTop: 20,
    gap: 12,
  },

  // Modern Meal Card
  modernMealCard: {
    borderRadius: 16,
    overflow: 'hidden' as const,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 6,
  },
  mealCardGradient: {
    padding: 16,
  },
  mealCardHeader: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 12,
  },
  modernMealIcon: {
    marginRight: 12,
  },
  iconGradient: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  mealCardInfo: {
    flex: 1,
  },
  modernMealType: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 2,
  },
  modernMealTime: {
    fontSize: 14,
    color: '#6B7280',
  },
  mealStats: {
    alignItems: 'center' as const,
  },
  calorieCount: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#6B7C5A',
  },
  calorieLabel: {
    fontSize: 12,
    color: '#6B7280',
  },
  modernMealName: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 16,
  },
  mealActions: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  primaryAction: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden' as const,
  },
  actionGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: 'white',
  },
  secondaryAction: {
    flex: 1,
    backgroundColor: 'rgba(107, 124, 90, 0.1)',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    gap: 8,
  },
  secondaryActionText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },

  // Empty State
  modernEmptyState: {
    margin: 20,
    borderRadius: 24,
    overflow: 'hidden' as const,
  },
  emptyStateGradient: {
    padding: 40,
    alignItems: 'center' as const,
  },
  modernEmptyTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginTop: 20,
    marginBottom: 8,
  },
  modernEmptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center' as const,
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyStateButton: {
    borderRadius: 16,
    overflow: 'hidden' as const,
  },
  emptyButtonGradient: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: 'white',
  },

  // Modal Styles
  modalBlur: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    padding: 20,
  },
  modernModalContainer: {
    width: width - 40,
    maxHeight: height * 0.9,
    borderRadius: 24,
    overflow: 'hidden' as const,
    backgroundColor: 'white',
  },
  modalGradient: {
    flex: 1,
  },
  modernModalHeader: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'flex-start' as const,
    padding: 24,
    paddingBottom: 16,
  },
  modalHeaderContent: {
    flex: 1,
  },
  modernModalTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: '#1F2937',
    marginBottom: 4,
  },
  modernModalSubtitle: {
    fontSize: 16,
    color: '#6B7280',
  },
  modernModalClose: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernModalContent: {
    flex: 1,
    paddingHorizontal: 24,
  },
  modernSection: {
    marginBottom: 24,
  },
  modernSectionTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: '#1F2937',
    marginBottom: 12,
  },
  modernTextInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minHeight: 80,
    textAlignVertical: 'top' as const,
  },
  nutritionGrid: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  nutritionItem: {
    flex: 1,
  },
  nutritionLabel: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7280',
    marginBottom: 8,
  },
  nutritionInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    textAlign: 'center' as const,
  },
  modernTagsGrid: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  modernTag: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    gap: 6,
  },
  modernTagSelected: {
    backgroundColor: '#6B7C5A',
  },
  modernTagText: {
    fontSize: 14,
    fontWeight: '500' as const,
    color: '#6B7C5A',
  },
  modernTagTextSelected: {
    color: 'white',
  },
  preferenceRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  preferenceLabel: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: '#1F2937',
  },
  modernCountButtons: {
    flexDirection: 'row' as const,
    gap: 8,
  },
  modernCountButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernCountButtonSelected: {
    backgroundColor: '#6B7C5A',
  },
  modernCountText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7C5A',
  },
  modernCountTextSelected: {
    color: 'white',
  },
  modernModalActions: {
    flexDirection: 'row' as const,
    padding: 24,
    paddingTop: 16,
    gap: 12,
  },
  modernCancelButton: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  },
  modernCancelText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#6B7280',
  },
  modernSaveButton: {
    flex: 2,
    borderRadius: 16,
    overflow: 'hidden' as const,
    backgroundColor: '#6B7C5A',
  },
  saveButtonGradient: {
    paddingVertical: 16,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    backgroundColor: '#6B7C5A',
  },
  modernSaveText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: 'white',
  },
};

export default PlanScreenModern;
